package com.smaile.health.domain.projection;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;

import java.util.UUID;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/8/2025
 **/
public interface OrganizationProjection {

    UUID getId();

    String getName();

    OrganizationType getType();

    OrganizationStatus getStatus();

    RoleProjection getRole();

}
