package com.smaile.health.security.authentication;

import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.security.authorization.OrganizationRoleGrantedAuthority;
import com.smaile.health.security.context.OrganizationPermissionContext;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.util.Assert;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * SMAILE Authentication represents an authenticated user in the SMAILE Health platform.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Getter
@Builder
@Slf4j
public final class SmaileAuthentication implements Authentication, Serializable {

    @Serial
    private static final long serialVersionUID = -907777220768819511L;

    private final transient UserProjection principal;
    private final String ownerRole;
    private final UUID ownerOrganizationId;
    private final SmaileAuthenticationDetails details;
    private final transient Map<UUID, OrganizationPermissionContext> organizationPermissionContexts;
    private final boolean authenticated;

    // @formatter:off
    private SmaileAuthentication(UserProjection principal,
                                 String ownerRole,
                                 UUID ownerOrganizationId,
                                 SmaileAuthenticationDetails details,
                                 Map<UUID, OrganizationPermissionContext> organizationPermissionContexts,
                                 boolean authenticated) {
        Assert.notNull(principal, "Principal cannot be null");

        this.principal = principal;
        this.ownerRole = ownerRole;
        this.ownerOrganizationId = ownerOrganizationId;
        this.details = details;
        this.organizationPermissionContexts = organizationPermissionContexts;
        this.authenticated = authenticated;

        log.debug("Created SmaileAuthentication for user id: {} with organization role", principal);
    }
    // @formatter:on

    /**
     * Creates an authenticated SmaileAuthentication instance.
     *
     * @param principal          The authenticated user
     * @param ownerRole          The user's owner organization roles
     * @param permissionContexts The user's organization permission contexts
     * @return Authenticated SmaileAuthentication instance
     */
    public static SmaileAuthentication authenticated(UserProjection principal, String ownerRole,
                                                     Map<UUID, OrganizationPermissionContext> permissionContexts) {
        Assert.notNull(principal, "Principal cannot be null for authenticated instance");
        SmaileAuthenticationDetails details = SmaileAuthenticationDetails.builder()
                .keycloakId(principal.getKeycloakId())
                .email(principal.getEmail())
                .authenticationMethod("KEYCLOAK_HEADER")
                .successful(true)
                .build();
        return SmaileAuthentication.builder()
                .ownerOrganizationId(principal.getOwnerOrganization().getId())
                .organizationPermissionContexts(permissionContexts)
                .principal(principal)
                .ownerRole(ownerRole)
                .details(details)
                .authenticated(true)
                .build();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<OrganizationRoleGrantedAuthority> authorities = new ArrayList<>();
        for (Map.Entry<UUID, OrganizationPermissionContext> organizationPermissionContext : organizationPermissionContexts.entrySet()) {
            UUID organizationId = organizationPermissionContext.getKey();
            String role = organizationPermissionContext.getValue().getRole();
            authorities.add(new OrganizationRoleGrantedAuthority(organizationId, role));
        }
        return authorities;
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getDetails() {
        return details;
    }

    @Override
    public Object getPrincipal() {
        return principal;
    }

    @Override
    public boolean isAuthenticated() {
        return authenticated;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                    "Cannot set authentication to true - use authenticated() factory method instead");
        }
        log.debug("Authentication invalidated for user: {}", getName());
    }

    @Override
    public String getName() {
        return details != null ? details.getEmail() : "unknown";
    }

    /**
     * Gets the user's unique identifier.
     *
     * @return User's Keycloak ID or null if not available
     */
    public UUID getUserId() {
        return principal.getId();
    }

    /**
     * Gets the user's email address.
     *
     * @return User's email or null if not available
     */
    public String getEmail() {
        return details != null ? details.getEmail() : null;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        SmaileAuthentication that = (SmaileAuthentication) obj;
        return authenticated == that.authenticated && Objects.equals(principal, that.principal) && Objects.equals(
                ownerRole, that.ownerRole);
    }

    @Override
    public int hashCode() {
        return Objects.hash(principal, ownerRole, authenticated);
    }

    @Override
    public String toString() {
        return String.format("SmaileAuthentication{user='%s', authenticated=%s, role=%s}", getEmail(), authenticated,
                ownerRole);
    }

}
