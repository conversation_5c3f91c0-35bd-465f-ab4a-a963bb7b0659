package com.smaile.health.security.authentication;

import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.exception.SmaileAuthenticationException;
import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.context.SecurityContextBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Responsible for building and managing authentication context
 * with organization-specific permissions after successful authentication.
 * <p>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Slf4j
public class SmaileAuthenticationService {

    private final SecurityContextBuilder securityContextBuilder;

    /**
     * Build complete authentication context for a user after successful authentication.
     * Creates an immutable SmaileAuthentication with organization-specific permissions.
     *
     * @param user The authenticated user
     * @return Immutable SmaileAuthentication with complete context
     * @throws SmaileAuthenticationException if context building fails
     */
    @Transactional
    public SmaileAuthentication buildAuthenticationContext(UserProjection user) {
        Assert.notNull(user, "User cannot be null");
        Assert.notNull(user.getEmail(), "User email cannot be null");

        log.debug("Building authentication context for user: {}", user.getEmail());

        try {
            Map<UUID, OrganizationPermissionContext> permissionContexts = buildOrganizationPermissionContexts(user);
            String ownerRole = user.getOwnerOrganization().getRole().getCode();
            return SmaileAuthentication.authenticated(user, ownerRole, permissionContexts);

        } catch (Exception e) {
            log.error("Failed to build authentication context for user: {}", user.getEmail(), e);
            throw new SmaileAuthenticationException("Failed to build authentication context", e);
        }
    }

    /**
     * Build organization permission contexts for a user with caching.
     *
     * @param user The user to build contexts for
     * @return Map of organization ID to permission context
     */
    public Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(UserProjection user) {
        Assert.notNull(user, "User cannot be null");

        log.debug("Building organization permission contexts for user: {}", user.getEmail());
        try {
            Map<UUID, OrganizationPermissionContext> contexts =
                    securityContextBuilder.buildOrganizationPermissionContexts(user);
            log.debug("Built {} organization permission contexts for user: {}",
                    contexts.size(), user.getEmail());
            return contexts;
        } catch (Exception e) {
            log.error("Failed to build organization permission contexts for user: {}", user.getEmail(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * Get cached organization permission contexts for a user.
     *
     * @param authentication The user's authentication
     * @return Map of organization permission contexts
     */
    public Map<UUID, OrganizationPermissionContext> getOrganizationPermissionContexts(SmaileAuthentication authentication) {
        Assert.notNull(authentication, "Authentication cannot be null");
        Assert.isTrue(authentication.isAuthenticated(), "User must be authenticated");
        UserProjection user = (UserProjection) authentication.getPrincipal();
        return buildOrganizationPermissionContexts(user);
    }

    /**
     * Get all organizations where user has a specific role.
     *
     * @param auth The user's authentication
     * @param role The role to search for
     * @return Set of organization IDs where user has the role
     */
    public Set<UUID> getOrganizationsWithRole(SmaileAuthentication auth, String role) {
        if (auth == null || !auth.isAuthenticated() || role == null || role.trim().isEmpty()) {
            return Collections.emptySet();
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = getOrganizationPermissionContexts(auth);
            Set<UUID> organizations = contexts.entrySet().stream()
                    .filter(entry -> entry.getValue().hasRole(role))
                    .map(Map.Entry::getKey)
                    .collect(java.util.stream.Collectors.toUnmodifiableSet());

            log.debug("Found {} organizations with role '{}' for user: {}",
                    organizations.size(), role, auth.getEmail());

            return organizations;
        } catch (Exception e) {
            log.warn("Error getting organizations with role for user {}: {}",
                    auth.getEmail(), e.getMessage());
            return Collections.emptySet();
        }
    }

}
