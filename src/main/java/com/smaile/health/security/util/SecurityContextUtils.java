package com.smaile.health.security.util;

import com.smaile.health.constants.RoleEnum;
import com.smaile.health.security.authentication.SmaileAuthentication;
import com.smaile.health.security.context.OrganizationPermissionContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Utility class for easy access to security context and permission checking.
 */
@Slf4j
public final class SecurityContextUtils {

    private SecurityContextUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Get current SmaileAuthentication
     */
    public static Optional<SmaileAuthentication> getCurrentAuthentication() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth instanceof SmaileAuthentication smaileAuthentication) {
                return Optional.of(smaileAuthentication);
            }
        } catch (Exception e) {
            log.debug("Failed to get current authentication", e);
        }
        return Optional.empty();
    }

    /**
     * Get permission context for specific organization
     */
    public static Optional<OrganizationPermissionContext> getOrganizationContext(UUID organizationId) {
        return Optional.ofNullable(getOrganizationContexts().get(organizationId));
    }

    /**
     * Get owner organization ID
     */
    public static Optional<UUID> getOwnerOrganizationId() {
        return getCurrentAuthentication().map(SmaileAuthentication::getOwnerOrganizationId);
    }

    /**
     * Get permission context for owner organization
     */
    public static Optional<OrganizationPermissionContext> getOwnerOrganizationContext() {
        return getOwnerOrganizationId().flatMap(SecurityContextUtils::getOrganizationContext);
    }

    /**
     * Check if current user is super admin
     */
    public static boolean isSuperAdmin() {
        return getCurrentAuthentication()
                .map(auth -> auth.getOwnerRole() != null &&
                        auth.getOwnerRole().equals(RoleEnum.SUPER_SMAILE_ADMIN.name()))
                .orElse(false);
    }

    /**
     * Get user's permission level in organization (for UI purposes)
     */
    public static String getPermissionLevel(UUID organizationId) {
        return getCurrentAuthentication()
                .map(auth -> {
                    if (isSuperAdmin()) {
                        return "SUPER_ADMIN";
                    }

                    OrganizationPermissionContext context = auth.getOrganizationPermissionContexts()
                            .get(organizationId);
                    if (context == null) {
                        return "NONE";
                    }

                    if (context.getRole().equals("ADMIN")) {
                        return "ADMIN";
                    }
                    if (context.getPermissions().stream().anyMatch(perm -> perm.contains("manage"))) {
                        return "MANAGE";
                    }
                    if (context.getPermissions().stream().anyMatch(perm ->
                            perm.contains("create") || perm.contains("update") || perm.contains("delete"))) {
                        return "WRITE";
                    }
                    if (context.getPermissions().stream().anyMatch(perm ->
                            perm.contains("read") || perm.contains("view"))) {
                        return "READ";
                    }

                    return "INHERITED";
                })
                .orElse("NONE");
    }

    private static Map<UUID, OrganizationPermissionContext> getOrganizationContexts() {
        return getCurrentAuthentication()
                .map(SmaileAuthentication::getOrganizationPermissionContexts)
                .orElse(Map.of());
    }

}
