package com.smaile.health.security.authorization;

import org.springframework.security.core.GrantedAuthority;

import java.util.UUID;

/**
 * Stores a representation of an authority granted to the
 * {@link com.smaile.health.security.authentication.SmaileAuthentication} object.
 *
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 27/8/2025
 **/
public final class OrganizationRoleGrantedAuthority implements GrantedAuthority {

    private final UUID organizationId;

    private final String role;

    @Override
    public String getAuthority() {
        return String.format("%s:%s", organizationId, role);
    }

    public OrganizationRoleGrantedAuthority(UUID organizationId, String role) {
        this.organizationId = organizationId;
        this.role = role;
    }

}
