package com.smaile.health.security.authorization;

import com.smaile.health.security.authentication.SmaileAuthentication;
import com.smaile.health.security.context.OrganizationPermissionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Service responsible for authorization decisions in the SMAILE Health platform.
 * <p>
 * This service follows the Single Responsibility Principle by handling only
 * authorization concerns, separate from authentication. It provides methods
 * to check permissions and roles within organizational contexts.
 * <p>
 * Key Features:
 * - Organization-specific permission checking
 * - Role-based access control (RBAC)
 * - Null-safe operations with defensive programming
 * - Comprehensive logging for security auditing
 * - Thread-safe operations
 * <p>
 * Security Considerations:
 * - All methods are null-safe and fail securely (deny by default)
 * - Comprehensive audit logging for security monitoring
 * - Input validation to prevent injection attacks
 * - Defensive programming against malicious inputs
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Service("smaileAuthorizationService")
@RequiredArgsConstructor
@Slf4j
public class SmaileAuthorizationService {

    /**
     * Checks if the authenticated user has a specific permission in an organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @param permission     The permission to verify
     * @return true if user has the permission, false otherwise
     */
    public boolean hasPermissionInOrganization(SmaileAuthentication authentication,
                                               UUID organizationId,
                                               String permission) {
        if (isInvalidInput(authentication, organizationId, permission)) {
            return false;
        }

        try {
            Optional<OrganizationPermissionContext> context = getOrganizationContext(authentication, organizationId);
            boolean hasPermission = context.map(ctx -> ctx.hasPermission(permission)).orElse(false);

            log.debug("Permission check: user={}, org={}, permission={}, result={}", authentication.getEmail(),
                    organizationId, permission, hasPermission);

            return hasPermission;
        } catch (Exception e) {
            log.warn("Error checking permission for user {} in organization {}: {}", authentication.getEmail(),
                    organizationId, e.getMessage());
            return false;
        }
    }

    /**
     * Checks if the authenticated user has a specific role in an organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @param role           The role to verify
     * @return true if user has the role, false otherwise
     */
    public boolean hasRoleInOrganization(SmaileAuthentication authentication, UUID organizationId, String role) {
        if (isInvalidInput(authentication, organizationId, role)) {
            return false;
        }

        try {
            Optional<OrganizationPermissionContext> context = getOrganizationContext(authentication, organizationId);
            boolean hasRole = context.map(ctx -> ctx.hasRole(role)).orElse(false);

            log.debug("Role check: user={}, org={}, role={}, result={}", authentication.getEmail(), organizationId,
                    role, hasRole);

            return hasRole;
        } catch (Exception e) {
            log.warn("Error checking role for user {} in organization {}: {}", authentication.getEmail(),
                    organizationId, e.getMessage());
            return false;
        }
    }

    /**
     * Gets the permission context for a specific organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID
     * @return Optional containing the permission context if available
     */
    private Optional<OrganizationPermissionContext> getOrganizationContext(SmaileAuthentication authentication,
                                                                           UUID organizationId) {
        if (authentication == null || !authentication.isAuthenticated() || organizationId == null) {
            return Optional.empty();
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = authentication.getOrganizationPermissionContexts();
            return Optional.ofNullable(contexts.get(organizationId));
        } catch (Exception e) {
            log.warn("Error getting organization context for user {} in organization {}: {}", authentication.getEmail(),
                    organizationId, e.getMessage());
            return Optional.empty();
        }
    }

    private boolean isInvalidInput(SmaileAuthentication authentication, UUID organizationId, String value) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.debug("Invalid authentication object provided");
            return true;
        }

        if (organizationId == null) {
            log.debug("Organization ID cannot be null");
            return true;
        }

        if (value == null || value.trim().isEmpty()) {
            log.debug("Permission/role value cannot be null or empty");
            return true;
        }

        return false;
    }

}
