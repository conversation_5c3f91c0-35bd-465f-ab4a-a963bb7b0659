package com.smaile.health.security.authorization;

import com.smaile.health.constants.RoleEnum;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.security.authentication.SmaileAuthentication;
import com.smaile.health.security.model.SmaileSecurityPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.UUID;

/**
 * Smaile permission evaluator with fine-grained permission checking.
 * Supports hierarchical permissions, organizational scope, and comprehensive audit logging.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmailePermissionEvaluator implements PermissionEvaluator {

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (!(authentication instanceof SmaileAuthentication smaileAuthentication)) {
            log.warn("Authentication is not SmaileAuthentication type");
            return false;
        }

        try {
            return evaluatePermission(smaileAuthentication, targetDomainObject, permission);
        } catch (Exception e) {
            log.error("Error evaluating permission: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean hasPermission(Authentication authentication,
                                 Serializable targetId,
                                 String targetType,
                                 Object permission) {
        if (!(authentication instanceof SmaileAuthentication customAuth)) {
            log.warn("Authentication is not SmaileAuthentication type");
            return false;
        }

        try {
            return evaluatePermissionById(customAuth, targetId, targetType, permission);
        } catch (Exception e) {
            log.error("Error evaluating permission by ID: {}", e.getMessage(), e);
            return false;
        }
    }

    private boolean evaluatePermission(SmaileAuthentication authentication,
                                       Object targetDomainObject,
                                       Object permission) {
        UserProjection user = (UserProjection) authentication.getPrincipal();

        if (isSuperAdmin(authentication)) {
            logPermissionCheck(user, permission.toString(), true, "Super admin access");
            return true;
        }

        SmaileSecurityPermission smaileSecurityPermission;
        try {
            smaileSecurityPermission = SmaileSecurityPermission.fromString(permission.toString());
        } catch (Exception e) {
            log.warn("Invalid permission format: {}", permission);
            logPermissionCheck(user, permission.toString(), false, "Invalid permission format");
            return false;
        }

        UUID organizationId = extractOrganizationId(targetDomainObject);
        if (organizationId == null && requiresOrganizationContext(smaileSecurityPermission)) {
            log.warn("Missing organization context for permission: {}", permission);
            logPermissionCheck(user, permission.toString(), false, "Missing organization context");
            return false;
        }

        boolean hasPermission = true;
        //                userPermissions.stream().anyMatch(p -> permissionImplies(p, smaileSecurityPermission, organizationId, user));
        //
        //        if (!hasPermission && organizationId != null) {
        //            hasPermission = checkInheritedPermissionsFromOrganizations(user, smaileSecurityPermission, organizationId);
        //        }
        //
        //        String reason = hasPermission ? "Permission granted" : "Permission denied";
        //        logPermissionCheck(user, permission.toString(), hasPermission, reason);

        return hasPermission;
    }

    private boolean evaluatePermissionById(SmaileAuthentication authentication, Serializable targetId,
                                           String targetType, Object permission) {
        UUID organizationId = null;
        if (targetId != null) {
            try {
                organizationId = UUID.fromString(targetId.toString());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid target ID format: {}", targetId);
            }
        }

        return evaluatePermission(authentication, organizationId, permission);
    }

    private boolean isSuperAdmin(SmaileAuthentication authentication) {
        return RoleEnum.SUPER_SMAILE_ADMIN.name().equals(authentication.getOwnerRole());
    }

    private UUID extractOrganizationId(Object targetDomainObject) {
        if (targetDomainObject == null) {
            return null;
        }

        if (targetDomainObject instanceof UUID) {
            return (UUID) targetDomainObject;
        }

        if (targetDomainObject instanceof String) {
            try {
                return UUID.fromString((String) targetDomainObject);
            } catch (IllegalArgumentException e) {
                log.debug("Target domain object is not a valid UUID: {}", targetDomainObject);
            }
        }

        if (targetDomainObject instanceof Organization org) {
            return org.getId();
        }

        return null;
    }

    private boolean requiresOrganizationContext(SmaileSecurityPermission permission) {
        return switch (permission.getScope()) {
            case ORGANIZATION, CHILD_ORGANIZATIONS, ORGANIZATION_TREE, SELF, TEAM, BRANCH -> true;
            case GLOBAL, NONE, CUSTOM -> false;
        };
    }

    private void logPermissionCheck(UserProjection user, String permission, boolean granted, String reason) {
        log.debug("Permission check: {} for user {} - {} ({})",
                permission, user.getEmail(), granted ? "GRANTED" : "DENIED", reason);
    }

}
