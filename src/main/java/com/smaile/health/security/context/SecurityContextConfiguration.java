package com.smaile.health.security.context;

//import com.smaile.health.security.service.OrganizationPermissionEvaluator;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;

/**
 * Configuration for security context and permission evaluation.
 */
@Configuration
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityContextConfiguration {

//    private final OrganizationPermissionEvaluator organizationPermissionEvaluator;

    /**
     * Configure method security expression handler with custom permission evaluator
     */
//    @Bean
//    public MethodSecurityExpressionHandler methodSecurityExpressionHandler() {
//        DefaultMethodSecurityExpressionHandler expressionHandler = new DefaultMethodSecurityExpressionHandler();
//        expressionHandler.setPermissionEvaluator(organizationPermissionEvaluator);
//        return expressionHandler;
//    }

}
