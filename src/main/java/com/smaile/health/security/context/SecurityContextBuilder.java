package com.smaile.health.security.context;

import com.smaile.health.domain.projection.OrganizationProjection;
import com.smaile.health.domain.projection.PermissionProjection;
import com.smaile.health.domain.projection.RoleProjection;
import com.smaile.health.domain.projection.UserProjection;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service responsible for building security context with permissions for each organization
 * after successful authentication.
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Slf4j
public class SecurityContextBuilder {

    /**
     * Build organization-specific permission contexts for a user
     */
    public Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(UserProjection user) {
        log.debug("Building permission contexts for user: {}", user.getEmail());
        Map<UUID, OrganizationPermissionContext> contexts = new HashMap<>();
        // Build context for owner organization
        OrganizationProjection ownerOrganization = user.getOwnerOrganization();
        contexts.put(ownerOrganization.getId(),
                buildPermissionContext(ownerOrganization, ownerOrganization.getRole(), true));
        // Build context for delegate organizations
        user.getDelegateOrganizations().forEach(organization ->
                contexts.put(organization.getId(),
                        buildPermissionContext(organization, organization.getRole(), false)));
        log.debug("Built {} permission contexts for user: {}", contexts.size(), user.getEmail());
        return contexts;
    }

    /**
     * Build permission context for direct role assignments in an organization
     */
    private OrganizationPermissionContext buildPermissionContext(OrganizationProjection organization,
                                                                 RoleProjection role, boolean isOwner) {

        return OrganizationPermissionContext.builder()
                .organizationId(organization.getId())
                .organizationName(organization.getName())
                .isOwner(isOwner)
                .permissions(role.getPermissions().stream()
                        .map(this::formatPermission)
                        .collect(Collectors.toSet()))
                .role(role.getCode())
                .build();
    }

    /**
     * Format permission as string
     */
    private String formatPermission(PermissionProjection permission) {
        StringBuilder sb = new StringBuilder();

        if (permission.getResource() != null) {
            sb.append(permission.getResource());
        }

        if (permission.getSubResource() != null) {
            sb.append(":").append(permission.getSubResource());
        }

        if (permission.getAction() != null) {
            sb.append(":").append(permission.getAction());
        }

        return sb.toString();
    }

}
