package com.smaile.health.repository.impl;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.UserStatus;
import com.smaile.health.domain.projection.OrganizationProjection;
import com.smaile.health.domain.projection.PermissionProjection;
import com.smaile.health.domain.projection.RoleProjection;
import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.repository.UserRepositoryCustom;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class UserRepositoryCustomImpl implements UserRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public UserProjection findUserProjectionByKeycloakId(String keycloakId) {
        String jpql = """
            SELECT
                u.id AS userId,
                u.keycloakId AS keycloakId,
                u.email AS email,
                u.fullName AS fullName,
                u.phone AS phone,
                u.status AS userStatus,
                uo.organization.id AS organizationId,
                uo.organization.name AS organizationName,
                uo.organization.type AS organizationType,
                uo.organization.status AS organizationStatus,
                ur.role.id AS roleId,
                ur.role.name AS roleName,
                ur.role.code AS roleCode,
                p.id AS permissionId,
                p.name AS permissionName,
                p.resource AS resource,
                p.subResource AS subResource,
                p.action AS action,
                p.description AS permissionDescription,
                CASE
                    WHEN u.organization.id = uo.organization.id THEN 'owner'
                    ELSE 'delegate'
                END AS userType
            FROM User u
            INNER JOIN UserOrganization uo ON u.id = uo.user.id
            INNER JOIN UserRole ur ON ur.userOrganization.id = uo.id
            INNER JOIN ur.role.permissions p
            WHERE u.keycloakId = :keycloakId
            """;

        List<Tuple> results = entityManager.createQuery(jpql, Tuple.class)
                .setParameter("keycloakId", keycloakId)
                .getResultList();

        if (results.isEmpty()) {
            return null;
        }

        return mapToUserProjectionWithAliases(results);
    }

    private UserProjection mapToUserProjectionWithAliases(List<Tuple> results) {
        Map<UUID, List<Tuple>> orgGroups = results.stream()
                .collect(Collectors.groupingBy(tuple -> tuple.get("organizationId", UUID.class)));

        Tuple firstRow = results.get(0);
        UUID userId = firstRow.get("userId", UUID.class);
        UUID keycloakId = UUID.fromString(firstRow.get("keycloakId", String.class));
        String email = firstRow.get("email", String.class);
        String fullName = firstRow.get("fullName", String.class);
        String phone = firstRow.get("phone", String.class);
        UserStatus userStatus = firstRow.get("userStatus", UserStatus.class);

        List<OrganizationProjection> delegateOrgs = new ArrayList<>();
        OrganizationProjection ownerOrg = null;

        for (Map.Entry<UUID, List<Tuple>> orgEntry : orgGroups.entrySet()) {
            List<Tuple> orgRows = orgEntry.getValue();
            OrganizationProjection org = buildOrganizationProjectionWithAliases(orgRows);

            // Check userType from the CASE statement
            String userType = orgRows.get(0).get("userType", String.class);

            if ("owner".equals(userType)) {
                ownerOrg = org;
            } else {
                delegateOrgs.add(org);
            }
        }

        final OrganizationProjection finalOwnerOrg = ownerOrg;
        final List<OrganizationProjection> finalDelegateOrgs = delegateOrgs;

        return new UserProjection() {
            @Override
            public UUID getId() {
                return userId;
            }

            @Override
            public UUID getKeycloakId() {
                return keycloakId;
            }

            @Override
            public String getEmail() {
                return email;
            }

            @Override
            public String getFullName() {
                return fullName;
            }

            @Override
            public String getPhone() {
                return phone;
            }

            @Override
            public UserStatus getStatus() {
                return userStatus;
            }

            @Override
            public OrganizationProjection getOwnerOrganization() {
                return finalOwnerOrg;
            }

            @Override
            public List<OrganizationProjection> getDelegateOrganizations() {
                return finalDelegateOrgs;
            }
        };
    }

    private OrganizationProjection buildOrganizationProjectionWithAliases(List<Tuple> orgRows) {
        Tuple firstRow = orgRows.get(0);
        UUID orgId = firstRow.get("organizationId", UUID.class);
        String orgName = firstRow.get("organizationName", String.class);
        OrganizationType orgType = firstRow.get("organizationType", OrganizationType.class);
        OrganizationStatus orgStatus = firstRow.get("organizationStatus", OrganizationStatus.class);

        // Group by role
        Map<UUID, List<Tuple>> roleGroups = orgRows.stream()
                .collect(Collectors.groupingBy(tuple -> tuple.get("roleId", UUID.class)));

        // Build role with permissions (assuming one role per organization for simplicity)
        RoleProjection role = null;
        if (!roleGroups.isEmpty()) {
            Map.Entry<UUID, List<Tuple>> roleEntry = roleGroups.entrySet().iterator().next();
            role = buildRoleProjectionWithAliases(roleEntry.getValue());
        }

        final RoleProjection finalRole = role;

        return new OrganizationProjection() {
            @Override
            public UUID getId() {
                return orgId;
            }

            @Override
            public String getName() {
                return orgName;
            }

            @Override
            public OrganizationType getType() {
                return orgType;
            }

            @Override
            public OrganizationStatus getStatus() {
                return orgStatus;
            }

            @Override
            public RoleProjection getRole() {
                return finalRole;
            }
        };
    }

    private RoleProjection buildRoleProjectionWithAliases(List<Tuple> roleRows) {
        Tuple firstRow = roleRows.get(0);
        String roleName = firstRow.get("roleName", String.class);
        String roleCode = firstRow.get("roleCode", String.class);

        // Build permissions
        List<PermissionProjection> permissions = roleRows.stream()
                .map(this::buildPermissionProjectionWithAliases)
                .distinct()
                .collect(Collectors.toList());

        return new RoleProjection() {
            @Override
            public String getName() {
                return roleName;
            }

            @Override
            public String getCode() {
                return roleCode;
            }

            @Override
            public List<PermissionProjection> getPermissions() {
                return permissions;
            }
        };
    }

    private PermissionProjection buildPermissionProjectionWithAliases(Tuple tuple) {
        UUID permissionId = tuple.get("permissionId", UUID.class);
        String permissionName = tuple.get("permissionName", String.class);
        String resource = tuple.get("resource", String.class);
        String subResource = tuple.get("subResource", String.class);
        String action = tuple.get("action", String.class);
        String description = tuple.get("permissionDescription", String.class);

        return new PermissionProjection() {
            @Override
            public UUID getId() {
                return permissionId;
            }

            @Override
            public String getName() {
                return permissionName;
            }

            @Override
            public String getResource() {
                return resource;
            }

            @Override
            public String getSubResource() {
                return subResource;
            }

            @Override
            public String getAction() {
                return action;
            }

            @Override
            public String getDescription() {
                return description;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) return true;
                if (!(o instanceof PermissionProjection)) return false;
                PermissionProjection that = (PermissionProjection) o;
                return Objects.equals(getId(), that.getId());
            }

            @Override
            public int hashCode() {
                return Objects.hash(getId());
            }
        };
    }
}
