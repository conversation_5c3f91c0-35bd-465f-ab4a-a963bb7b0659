package com.smaile.health.util;

import com.smaile.health.security.authentication.SmaileAuthentication;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;
import java.util.UUID;

public final class SecurityUtils {

    private SecurityUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static SmaileAuthentication getActorContext() {
        return (SmaileAuthentication) SecurityContextHolder.getContext().getAuthentication();
    }

    public static Optional<UUID> getCurrentUserOrganizationId() {
        SmaileAuthentication authentication = getActorContext();
        if (authentication != null && authentication.isAuthenticated()) {
            return Optional.ofNullable(authentication.getOwnerOrganizationId());
        }
        return Optional.empty();
    }

    public static boolean hasRole(String role) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            return authentication.getAuthorities().stream()
                    .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals("ROLE_" + role));
        }
        return false;
    }

    public static boolean isAuthenticated() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && authentication.isAuthenticated();
    }

}