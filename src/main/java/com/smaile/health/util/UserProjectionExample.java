package com.smaile.health.util;

import com.smaile.health.domain.projection.OrganizationProjection;
import com.smaile.health.domain.projection.PermissionProjection;
import com.smaile.health.domain.projection.RoleProjection;
import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.service.UserContextService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Example usage of the UserProjection with owner/delegate organization logic
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserProjectionExample {

    private final UserContextService userContextService;

    public void demonstrateUsage(String keycloakId) {
        log.info("Getting user context for keycloakId: {}", keycloakId);
        
        UserProjection user = userContextService.getUserContext(keycloakId);
        
        if (user == null) {
            log.warn("No user found with keycloakId: {}", keycloakId);
            return;
        }
        
        log.info("User ID: {}, Email: {}, Status: {}", 
                user.getId(), user.getEmail(), user.getStatus());
        
        // Owner organization (determined by CASE statement)
        OrganizationProjection ownerOrg = user.getOwnerOrganization();
        if (ownerOrg != null) {
            log.info("Owner Organization: {} (ID: {}, Type: {}, Status: {})", 
                    ownerOrg.getName(), ownerOrg.getId(), ownerOrg.getType(), ownerOrg.getStatus());
            
            RoleProjection role = ownerOrg.getRole();
            if (role != null) {
                log.info("Role: {} (Code: {})", role.getName(), role.getCode());
                
                if (role.getPermissions() != null) {
                    log.info("Permissions count: {}", role.getPermissions().size());
                    for (PermissionProjection permission : role.getPermissions()) {
                        log.info("  - Permission: {} (Resource: {}, Action: {})", 
                                permission.getName(), permission.getResource(), permission.getAction());
                    }
                }
            }
        }
        
        // Delegate organizations
        if (user.getDelegateOrganizations() != null && !user.getDelegateOrganizations().isEmpty()) {
            log.info("Delegate Organizations count: {}", user.getDelegateOrganizations().size());
            for (OrganizationProjection delegateOrg : user.getDelegateOrganizations()) {
                log.info("Delegate Organization: {} (ID: {}, Type: {})", 
                        delegateOrg.getName(), delegateOrg.getId(), delegateOrg.getType());
            }
        } else {
            log.info("No delegate organizations found");
        }
    }
}
