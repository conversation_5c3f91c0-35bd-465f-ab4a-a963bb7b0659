package com.smaile.health.service;

import com.smaile.health.domain.projection.UserProjection;

/**
 * Service for building user context with roles and permissions
 */
public interface UserContextService {
    
    /**
     * Get user context including organizations, roles, and permissions by Keycloak ID
     * @param keycloakId the Keycloak user ID
     * @return UserProjection with nested organization, role, and permission data
     */
    UserProjection getUserContext(String keycloakId);
}
