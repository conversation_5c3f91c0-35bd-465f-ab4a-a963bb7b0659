package com.smaile.health.security.service;

import com.smaile.health.domain.User;
import com.smaile.health.security.authentication.SmaileAuthentication;
import com.smaile.health.security.authentication.SmaileAuthenticationDetails;
import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.context.SecurityContextBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Comprehensive test suite for the enhanced AuthenticationContextService.
 * Tests cover context building, caching, validation, and enterprise-level functionality.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthenticationContextService Tests")
class AuthenticationContextServiceTest {

    @Mock
    private SecurityContextBuilder securityContextBuilder;

    @InjectMocks
    private AuthenticationContextService authenticationContextService;

    private User testUser;
    private UUID orgId1;
    private UUID orgId2;
    private Map<UUID, OrganizationPermissionContext> mockContexts;

    @BeforeEach
    void setUp() {
        testUser = mock(User.class);
        when(testUser.getId()).thenReturn(UUID.randomUUID());
        when(testUser.getEmail()).thenReturn("<EMAIL>");
        when(testUser.getKeycloakId()).thenReturn("test-keycloak-id");

        orgId1 = UUID.randomUUID();
        orgId2 = UUID.randomUUID();

        // Create mock organization permission contexts
        OrganizationPermissionContext context1 = OrganizationPermissionContext.builder()
                .organizationId(orgId1)
                .organizationName("Hospital A")
                .permissions(Set.of("patient:read", "patient:write"))
                .roles(Set.of("DOCTOR", "USER"))
                .build();

        OrganizationPermissionContext context2 = OrganizationPermissionContext.builder()
                .organizationId(orgId2)
                .organizationName("Clinic B")
                .permissions(Set.of("patient:read", "appointment:manage"))
                .roles(Set.of("NURSE", "USER"))
                .build();

        mockContexts = Map.of(orgId1, context1, orgId2, context2);
    }

    @Nested
    @DisplayName("Authentication Context Building")
    class AuthenticationContextBuildingTests {

        @Test
        @DisplayName("Should build complete authentication context successfully")
        void shouldBuildCompleteAuthenticationContext() {
            // Given
            when(securityContextBuilder.buildOrganizationPermissionContexts(testUser))
                    .thenReturn(mockContexts);

            // When
            SmaileAuthentication result = authenticationContextService.buildAuthenticationContext(testUser);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.isAuthenticated()).isTrue();
            assertThat(result.getPrincipal()).isEqualTo(testUser);
            assertThat(result.getAllRoles()).containsExactlyInAnyOrder("DOCTOR", "USER", "NURSE");
            assertThat(result.getDetails()).isNotNull();
            assertThat(result.getAuthenticatedAt()).isNotNull();

            verify(securityContextBuilder).buildOrganizationPermissionContexts(testUser);
        }

        @Test
        @DisplayName("Should handle empty organization contexts gracefully")
        void shouldHandleEmptyOrganizationContexts() {
            // Given
            when(securityContextBuilder.buildOrganizationPermissionContexts(testUser))
                    .thenReturn(Map.of());

            // When
            SmaileAuthentication result = authenticationContextService.buildAuthenticationContext(testUser);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.isAuthenticated()).isTrue();
            assertThat(result.getAllRoles()).isEmpty();
        }

        @Test
        @DisplayName("Should throw exception for null user")
        void shouldThrowExceptionForNullUser() {
            // When & Then
            assertThatThrownBy(() -> authenticationContextService.buildAuthenticationContext(null))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("User cannot be null");
        }

    }

    @Nested
    @DisplayName("Organization Permission Context Management")
    class OrganizationPermissionContextTests {

        @Test
        @DisplayName("Should build organization permission contexts with caching")
        void shouldBuildOrganizationPermissionContextsWithCaching() {
            // Given
            when(securityContextBuilder.buildOrganizationPermissionContexts(testUser))
                    .thenReturn(mockContexts);

            // When
            Map<UUID, OrganizationPermissionContext> result1 =
                    authenticationContextService.buildOrganizationPermissionContexts(testUser);
            Map<UUID, OrganizationPermissionContext> result2 =
                    authenticationContextService.buildOrganizationPermissionContexts(testUser);

            // Then
            assertThat(result1).isEqualTo(mockContexts);
            assertThat(result2).isEqualTo(mockContexts);
            assertThat(result1).hasSize(2);
            assertThat(result1).containsKeys(orgId1, orgId2);

            // Should only call the builder once due to caching
            verify(securityContextBuilder, times(1)).buildOrganizationPermissionContexts(testUser);
        }

        @Test
        @DisplayName("Should get cached organization permission contexts")
        void shouldGetCachedOrganizationPermissionContexts() {
            // Given
            SmaileAuthentication auth = SmaileAuthentication.authenticated(
                    testUser, Set.of("USER"), mock(SmaileAuthenticationDetails.class));

            when(securityContextBuilder.buildOrganizationPermissionContexts(testUser))
                    .thenReturn(mockContexts);

            // First call to populate cache
            authenticationContextService.buildOrganizationPermissionContexts(testUser);

            // When
            Map<UUID, OrganizationPermissionContext> result =
                    authenticationContextService.getOrganizationPermissionContexts(auth);

            // Then
            assertThat(result).isEqualTo(mockContexts);
        }

    }

    @Nested
    @DisplayName("Context Refresh")
    class ContextRefreshTests {

        @Test
        @DisplayName("Should refresh authentication context successfully")
        void shouldRefreshAuthenticationContextSuccessfully() {
            // Given
            SmaileAuthentication currentAuth = SmaileAuthentication.authenticated(
                    testUser, Set.of("OLD_ROLE"), mock(SmaileAuthenticationDetails.class));

            when(securityContextBuilder.buildOrganizationPermissionContexts(testUser))
                    .thenReturn(mockContexts);

            // When
            SmaileAuthentication refreshedAuth =
                    authenticationContextService.refreshAuthenticationContext(currentAuth);

            // Then
            assertThat(refreshedAuth).isNotNull();
            assertThat(refreshedAuth.isAuthenticated()).isTrue();
            assertThat(refreshedAuth.getAllRoles()).containsExactlyInAnyOrder("DOCTOR", "USER", "NURSE");
            assertThat(refreshedAuth).isNotSameAs(currentAuth); // Should be a new instance
        }

        @Test
        @DisplayName("Should throw exception when refreshing null authentication")
        void shouldThrowExceptionWhenRefreshingNullAuthentication() {
            // When & Then
            assertThatThrownBy(() -> authenticationContextService.refreshAuthenticationContext(null))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Current authentication cannot be null");
        }

    }

    @Nested
    @DisplayName("Permission Checking")
    class PermissionCheckingTests {

        private SmaileAuthentication testAuth;

        @BeforeEach
        void setUp() {
            testAuth = SmaileAuthentication.authenticated(
                    testUser, Set.of("DOCTOR", "USER"), mock(SmaileAuthenticationDetails.class));

            when(securityContextBuilder.buildOrganizationPermissionContexts(testUser))
                    .thenReturn(mockContexts);
        }

        @Test
        @DisplayName("Should check permission in any organization correctly")
        void shouldCheckPermissionInAnyOrganizationCorrectly() {
            // When & Then
            assertThat(authenticationContextService.hasPermissionInAnyOrganization(testAuth, "patient:read"))
                    .isTrue();
            assertThat(authenticationContextService.hasPermissionInAnyOrganization(testAuth, "admin:manage"))
                    .isFalse();
        }

        @Test
        @DisplayName("Should get organizations with specific permission")
        void shouldGetOrganizationsWithSpecificPermission() {
            // When
            Set<UUID> orgsWithPatientRead =
                    authenticationContextService.getOrganizationsWithPermission(testAuth, "patient:read");
            Set<UUID> orgsWithAppointmentManage =
                    authenticationContextService.getOrganizationsWithPermission(testAuth, "appointment:manage");

            // Then
            assertThat(orgsWithPatientRead).containsExactlyInAnyOrder(orgId1, orgId2);
            assertThat(orgsWithAppointmentManage).containsExactly(orgId2);
        }

        @Test
        @DisplayName("Should get organizations with specific role")
        void shouldGetOrganizationsWithSpecificRole() {
            // When
            Set<UUID> orgsWithDoctor =
                    authenticationContextService.getOrganizationsWithRole(testAuth, "DOCTOR");
            Set<UUID> orgsWithNurse =
                    authenticationContextService.getOrganizationsWithRole(testAuth, "NURSE");

            // Then
            assertThat(orgsWithDoctor).containsExactly(orgId1);
            assertThat(orgsWithNurse).containsExactly(orgId2);
        }

        @Test
        @DisplayName("Should check administrative access correctly")
        void shouldCheckAdministrativeAccessCorrectly() {
            // Given - Add admin context
            OrganizationPermissionContext adminContext = OrganizationPermissionContext.builder()
                    .organizationId(UUID.randomUUID())
                    .organizationName("Admin Org")
                    .permissions(Set.of("user:manage", "system:admin"))
                    .roles(Set.of("ADMIN"))
                    .build();

            Map<UUID, OrganizationPermissionContext> contextsWithAdmin =
                    Map.of(orgId1, mockContexts.get(orgId1),
                            orgId2, mockContexts.get(orgId2),
                            adminContext.getOrganizationId(), adminContext);

            when(securityContextBuilder.buildOrganizationPermissionContexts(testUser))
                    .thenReturn(contextsWithAdmin);

            // When & Then
            assertThat(authenticationContextService.hasAdministrativeAccess(testAuth)).isTrue();
        }

    }

    @Nested
    @DisplayName("Context Validation")
    class ContextValidationTests {

        @Test
        @DisplayName("Should validate valid authentication context")
        void shouldValidateValidAuthenticationContext() {
            // Given
            SmaileAuthentication validAuth = SmaileAuthentication.authenticated(
                    testUser, Set.of("USER"), mock(SmaileAuthenticationDetails.class));

            when(securityContextBuilder.buildOrganizationPermissionContexts(testUser))
                    .thenReturn(mockContexts);

            // When & Then
            assertThat(authenticationContextService.validateAuthenticationContext(validAuth)).isTrue();
        }

        @Test
        @DisplayName("Should reject null authentication context")
        void shouldRejectNullAuthenticationContext() {
            // When & Then
            assertThat(authenticationContextService.validateAuthenticationContext(null)).isFalse();
        }

        @Test
        @DisplayName("Should reject unauthenticated context")
        void shouldRejectUnauthenticatedContext() {
            // Given
            SmaileAuthentication unauthenticatedAuth = SmaileAuthentication.unauthenticated(testUser);

            // When & Then
            assertThat(authenticationContextService.validateAuthenticationContext(unauthenticatedAuth)).isFalse();
        }

    }

    @Nested
    @DisplayName("Access Summary")
    class AccessSummaryTests {

        @Test
        @DisplayName("Should generate comprehensive access summary")
        void shouldGenerateComprehensiveAccessSummary() {
            // Given
            SmaileAuthentication testAuth = SmaileAuthentication.authenticated(
                    testUser, Set.of("DOCTOR", "USER"), mock(SmaileAuthenticationDetails.class));

            when(securityContextBuilder.buildOrganizationPermissionContexts(testUser))
                    .thenReturn(mockContexts);

            // When
            Map<String, Object> summary = authenticationContextService.getAccessSummary(testAuth);

            // Then
            assertThat(summary).isNotEmpty();
            assertThat(summary.get("userId")).isEqualTo(testUser.getId());
            assertThat(summary.get("userEmail")).isEqualTo(testUser.getEmail());
            assertThat(summary.get("keycloakId")).isEqualTo(testUser.getKeycloakId());
            assertThat(summary.get("totalOrganizations")).isEqualTo(2);
            assertThat(summary.get("totalPermissions")).isEqualTo(3); // patient:read, patient:write, appointment:manage
            assertThat(summary.get("totalRoles")).isEqualTo(3); // DOCTOR, USER, NURSE
            assertThat(summary.get("isAuthenticated")).isEqualTo(true);
            assertThat(summary.get("authenticatedAt")).isNotNull();
        }

        @Test
        @DisplayName("Should return empty summary for null authentication")
        void shouldReturnEmptySummaryForNullAuthentication() {
            // When
            Map<String, Object> summary = authenticationContextService.getAccessSummary(null);

            // Then
            assertThat(summary).isEmpty();
        }

    }

}
