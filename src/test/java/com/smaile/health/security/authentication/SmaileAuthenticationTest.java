package com.smaile.health.security.authentication;

import com.smaile.health.domain.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Nested;
import org.springframework.security.core.GrantedAuthority;

import java.time.Instant;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive test suite for the enhanced SmaileAuthentication class.
 * Tests cover SOLID principles, security features, and enterprise-level functionality.
 */
@DisplayName("SmaileAuthentication Tests")
class SmaileAuthenticationTest {

    private User mockUser;
    private Map<UUID, String> testRoles;
    private SmaileAuthenticationDetails testDetails;

    @BeforeEach
    void setUp() {
        mockUser = mock(User.class);
        when(mockUser.getEmail()).thenReturn("<EMAIL>");
        when(mockUser.getKeycloakId()).thenReturn("test-keycloak-id");
        when(mockUser.getFullName()).thenReturn("Test User");

        testRoles = Map.of(UUID.fromString("7adsf-adsf-adsf-adsf-adsf"), "USER",
                UUID.fromString("7sdsf-adsf-adsf-adsf-adsf"), "ADMIN",
                UUID.fromString("7ddsf-adsf-adsf-adsf-adsf"), "ORGANIZATION_MANAGER");
        testDetails = mock(SmaileAuthenticationDetails.class);
    }

    @Nested
    @DisplayName("Factory Methods")
    class FactoryMethodTests {

        @Test
        @DisplayName("Should create authenticated instance with all required fields")
        void shouldCreateAuthenticatedInstance() {
            // When
            SmaileAuthentication auth = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);

            // Then
            assertThat(auth).isNotNull();
            assertThat(auth.isAuthenticated()).isTrue();
            assertThat(auth.getPrincipal()).isEqualTo(mockUser);
            assertThat(auth.getAllRoles()).containsExactlyInAnyOrderElementsOf(testRoles);
            assertThat(auth.getDetails()).isEqualTo(testDetails);
            assertThat(auth.getAuthenticatedAt()).isNotNull();
            assertThat(auth.getAuthenticatedAt()).isBeforeOrEqualTo(Instant.now());
        }

        @Test
        @DisplayName("Should create unauthenticated instance")
        void shouldCreateUnauthenticatedInstance() {
            // When
            SmaileAuthentication auth = SmaileAuthentication.unauthenticated(mockUser);

            // Then
            assertThat(auth).isNotNull();
            assertThat(auth.isAuthenticated()).isFalse();
            assertThat(auth.getPrincipal()).isEqualTo(mockUser);
            assertThat(auth.getAllRoles()).isEmpty();
            assertThat(auth.getDetails()).isNull();
        }

        @Test
        @DisplayName("Should throw exception when creating authenticated instance with null user")
        void shouldThrowExceptionForNullUser() {
            // When & Then
            assertThatThrownBy(() -> SmaileAuthentication.authenticated(null, testRoles, testDetails))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Principal cannot be null");
        }
    }

    @Nested
    @DisplayName("Spring Security Interface Implementation")
    class SpringSecurityInterfaceTests {

        private SmaileAuthentication authenticatedAuth;

        @BeforeEach
        void setUp() {
            authenticatedAuth = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);
        }

        @Test
        @DisplayName("Should return correct authorities")
        void shouldReturnCorrectAuthorities() {
            // When
            Collection<? extends GrantedAuthority> authorities = authenticatedAuth.getAuthorities();

            // Then
            assertThat(authorities).hasSize(3);
            assertThat(authorities.stream().map(GrantedAuthority::getAuthority))
                    .containsExactlyInAnyOrder("USER", "ADMIN", "ORGANIZATION_MANAGER");
        }

        @Test
        @DisplayName("Should never return credentials")
        void shouldNeverReturnCredentials() {
            // When & Then
            assertThat(authenticatedAuth.getCredentials()).isNull();
        }

        @Test
        @DisplayName("Should return user email as name")
        void shouldReturnUserEmailAsName() {
            // When & Then
            assertThat(authenticatedAuth.getName()).isEqualTo("<EMAIL>");
        }

        @Test
        @DisplayName("Should handle null user gracefully in getName")
        void shouldHandleNullUserInGetName() {
            // Given
            SmaileAuthentication authWithNullUser = SmaileAuthentication.builder()
                    .principal(null)
                    .roles(Set.of())
                    .authenticated(false)
                    .authenticatedAt(Instant.now())
                    .build();

            // When & Then
            assertThat(authWithNullUser.getName()).isEqualTo("unknown");
        }

        @Test
        @DisplayName("Should throw exception when trying to set authenticated to true")
        void shouldThrowExceptionWhenSettingAuthenticatedToTrue() {
            // When & Then
            assertThatThrownBy(() -> authenticatedAuth.setAuthenticated(true))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Cannot set authentication to true");
        }
    }

    @Nested
    @DisplayName("Utility Methods")
    class UtilityMethodTests {

        private SmaileAuthentication authenticatedAuth;

        @BeforeEach
        void setUp() {
            authenticatedAuth = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);
        }

        @Test
        @DisplayName("Should check role membership correctly")
        void shouldCheckRoleMembershipCorrectly() {
            // When & Then
            assertThat(authenticatedAuth.hasRole("ADMIN")).isTrue();
            assertThat(authenticatedAuth.hasRole("USER")).isTrue();
            assertThat(authenticatedAuth.hasRole("SUPER_ADMIN")).isFalse();
        }

        @Test
        @DisplayName("Should check any role membership correctly")
        void shouldCheckAnyRoleMembershipCorrectly() {
            // When & Then
            assertThat(authenticatedAuth.hasAnyRole(Set.of("ADMIN", "SUPER_ADMIN"))).isTrue();
            assertThat(authenticatedAuth.hasAnyRole(Set.of("SUPER_ADMIN", "GUEST"))).isFalse();
            assertThat(authenticatedAuth.hasAnyRole(null)).isFalse();
        }

        @Test
        @DisplayName("Should return user ID correctly")
        void shouldReturnUserIdCorrectly() {
            // When & Then
            assertThat(authenticatedAuth.getUserId()).isEqualTo("test-keycloak-id");
        }

        @Test
        @DisplayName("Should return user email correctly")
        void shouldReturnUserEmailCorrectly() {
            // When & Then
            assertThat(authenticatedAuth.getEmail()).isEqualTo("<EMAIL>");
        }

        @Test
        @DisplayName("Should return immutable roles set")
        void shouldReturnImmutableRolesSet() {
            // When
            Set<String> roles = authenticatedAuth.getAllRoles();

            // Then
            assertThat(roles).containsExactlyInAnyOrderElementsOf(testRoles);
            assertThatThrownBy(() -> roles.add("NEW_ROLE"))
                    .isInstanceOf(UnsupportedOperationException.class);
        }
    }

    @Nested
    @DisplayName("Expiration Logic")
    class ExpirationLogicTests {

        @Test
        @DisplayName("Should not be expired within timeout period")
        void shouldNotBeExpiredWithinTimeoutPeriod() {
            // Given
            SmaileAuthentication auth = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);

            // When & Then
            assertThat(auth.isExpired(60)).isFalse(); // 60 minutes timeout
        }

        @Test
        @DisplayName("Should be expired for unauthenticated user")
        void shouldBeExpiredForUnauthenticatedUser() {
            // Given
            SmaileAuthentication auth = SmaileAuthentication.unauthenticated(mockUser);

            // When & Then
            assertThat(auth.isExpired(60)).isTrue();
        }

        @Test
        @DisplayName("Should handle zero timeout correctly")
        void shouldHandleZeroTimeoutCorrectly() {
            // Given
            SmaileAuthentication auth = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);

            // When & Then
            assertThat(auth.isExpired(0)).isTrue();
        }
    }

    @Nested
    @DisplayName("Object Methods")
    class ObjectMethodTests {

        @Test
        @DisplayName("Should implement equals correctly")
        void shouldImplementEqualsCorrectly() {
            // Given
            SmaileAuthentication auth1 = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);
            SmaileAuthentication auth2 = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);

            // When & Then
            assertThat(auth1).isNotEqualTo(auth2); // Different timestamps
            assertThat(auth1).isEqualTo(auth1); // Same instance
        }

        @Test
        @DisplayName("Should implement hashCode correctly")
        void shouldImplementHashCodeCorrectly() {
            // Given
            SmaileAuthentication auth = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);

            // When & Then
            assertThat(auth.hashCode()).isNotZero();
        }

        @Test
        @DisplayName("Should provide meaningful toString")
        void shouldProvideMeaningfulToString() {
            // Given
            SmaileAuthentication auth = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);

            // When
            String toString = auth.toString();

            // Then
            assertThat(toString).contains("SmaileAuthentication");
            assertThat(toString).contains("<EMAIL>");
            assertThat(toString).contains("authenticated=true");
            assertThat(toString).contains("roles=3");
        }
    }

    @Nested
    @DisplayName("Thread Safety")
    class ThreadSafetyTests {

        @Test
        @DisplayName("Should be thread-safe for concurrent access")
        void shouldBeThreadSafeForConcurrentAccess() throws InterruptedException {
            // Given
            SmaileAuthentication auth = SmaileAuthentication.authenticated(mockUser, testRoles, testDetails);
            
            // When - Multiple threads accessing the same authentication object
            Thread[] threads = new Thread[10];
            for (int i = 0; i < threads.length; i++) {
                threads[i] = new Thread(() -> {
                    assertThat(auth.isAuthenticated()).isTrue();
                    assertThat(auth.hasRole("ADMIN")).isTrue();
                    assertThat(auth.getAllRoles()).hasSize(3);
                });
            }

            for (Thread thread : threads) {
                thread.start();
            }

            for (Thread thread : threads) {
                thread.join();
            }

            // Then - No exceptions should be thrown and state should remain consistent
            assertThat(auth.isAuthenticated()).isTrue();
            assertThat(auth.getAllRoles()).hasSize(3);
        }
    }
}
