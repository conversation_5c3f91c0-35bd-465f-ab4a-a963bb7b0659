# Enhanced User Context Building Guide

## Overview

This guide demonstrates how the enhanced SMAILE authentication system builds user context with organization permissions and roles after successful authentication, following SOLID principles and KISS methodology.

## Architecture Overview

The user context building process involves several components working together:

1. **SmaileAuthenticationProvider** - Handles initial authentication
2. **AuthenticationContextService** - Builds complete user context with permissions
3. **SecurityContextBuilder** - Constructs organization-specific permission contexts
4. **SmaileAuthentication** - Immutable authentication object with user context
5. **SmaileAuthorizationService** - Handles authorization decisions

## Authentication Flow with Context Building

### 1. Initial Authentication Request

```java
// 1. User request comes through SmaileAuthenticationFilter
// 2. Filter extracts credentials from headers
AuthenticationCredentials credentials = extractCredentials(request);

// 3. Creates authentication token
SmaileAuthenticationToken token = new SmaileAuthenticationToken(
    credentials.getKeycloakId(),
    credentials.getEmail(), 
    credentials.getPreferredUsername()
);

// 4. Delegates to authentication provider
Authentication result = authenticationProvider.authenticate(token);
```

### 2. User Validation and Context Building

```java
@Component
public class SmaileAuthenticationProvider implements AuthenticationProvider {
    
    @Override
    public SmaileAuthentication authenticate(Authentication authentication) throws AuthenticationException {
        SmaileAuthenticationToken token = (SmaileAuthenticationToken) authentication;
        
        // 1. Validate credentials
        validateAuthenticationInput(token.getKeycloakId(), token.getEmail());
        
        // 2. Find and validate user
        User user = findUserByKeycloakId(token.getKeycloakId());
        validateUserAccount(user);
        
        // 3. Build complete authentication context with organization permissions
        SmaileAuthentication smaileAuthentication = 
            authenticationContextService.buildAuthenticationContext(user);
        
        // 4. Validate the result
        validateAuthenticationResult(smaileAuthentication, token.getKeycloakId());
        
        return smaileAuthentication;
    }
}
```

### 3. Organization Permission Context Building

```java
@Service
public class AuthenticationContextService {
    
    public SmaileAuthentication buildAuthenticationContext(User user) {
        // 1. Build organization-specific permission contexts
        Map<UUID, OrganizationPermissionContext> permissionContexts = 
            buildOrganizationPermissionContexts(user);
        
        // 2. Extract all roles from organization contexts
        Set<String> roles = extractRolesFromContexts(permissionContexts);
        
        // 3. Create authentication details
        SmaileAuthenticationDetails details = createAuthenticationDetails(user, permissionContexts);
        
        // 4. Create immutable authenticated instance
        SmaileAuthentication authentication = SmaileAuthentication.authenticated(user, roles, details);
        
        // 5. Cache permission contexts for performance
        cachePermissionContexts(user.getKeycloakId(), permissionContexts);
        
        return authentication;
    }
    
    @Cacheable(value = "organizationPermissionContexts", key = "#user.keycloakId")
    public Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(User user) {
        // Delegate to SecurityContextBuilder for actual context building
        return securityContextBuilder.buildOrganizationPermissionContexts(user);
    }
}
```

### 4. Security Context Builder Implementation

```java
@Service
public class SecurityContextBuilder {
    
    public Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(User user) {
        Map<UUID, OrganizationPermissionContext> contexts = new HashMap<>();
        
        // 1. Get all permissions mapped by role codes
        Multimap<String, Permission> permissionsByRoles = getPermissionsByRoles();
        
        // 2. Get active user roles across all organizations
        List<UserOrganizationRoles> activeUserRoles = getActiveUserRoles(user);
        
        // 3. Group user roles by organization
        ArrayListMultimap<UUID, UserOrganizationRoles> userRolesByOrganization = 
            activeUserRoles.stream().collect(Multimaps.toMultimap(
                UserOrganizationRoles::getOrganizationId,
                userOrganizationRoleProjections -> userOrganizationRoleProjections,
                ArrayListMultimap::create
            ));
        
        // 4. Build permission context for each organization
        userRolesByOrganization.asMap().forEach((orgId, userOrgRoles) -> {
            List<UserOrganizationRoles> userOrganizationRoleProjections = userOrgRoles.stream().toList();
            UUID organizationId = userOrganizationRoleProjections.get(0).getOrganizationId();
            String organizationName = userOrganizationRoleProjections.get(0).getOrganizationName();
            
            OrganizationPermissionContext context = buildDirectPermissionContext(
                organizationId, organizationName, userOrganizationRoleProjections, permissionsByRoles);
            contexts.put(orgId, context);
        });
        
        return contexts;
    }
    
    private OrganizationPermissionContext buildDirectPermissionContext(
            UUID organizationId, String organizationName,
            List<UserOrganizationRoles> userOrganizationRoleProjections,
            Multimap<String, Permission> userRoles) {
        
        Set<String> permissions = new HashSet<>();
        Set<String> roles = new HashSet<>();
        
        // Extract roles and their associated permissions
        for (UserOrganizationRoles userRole : userOrganizationRoleProjections) {
            String roleCode = userRole.getRoleCode();
            roles.add(roleCode);
            
            if (userRoles.containsKey(roleCode)) {
                Set<String> rolePermissions = userRoles.get(roleCode).stream()
                    .map(this::formatPermission)
                    .collect(Collectors.toSet());
                permissions.addAll(rolePermissions);
            }
        }
        
        return OrganizationPermissionContext.builder()
            .organizationId(organizationId)
            .organizationName(organizationName)
            .permissions(permissions)
            .roles(roles)
            .build();
    }
}
```

## Usage Examples

### 1. Accessing User Context After Authentication

```java
@RestController
public class UserController {
    
    @Autowired
    private SmaileAuthorizationService authorizationService;
    
    @GetMapping("/user/profile")
    public ResponseEntity<UserProfile> getUserProfile() {
        // Get current authenticated user
        Optional<SmaileAuthentication> authOpt = AuthenticationUtils.getCurrentAuthentication();
        
        if (authOpt.isEmpty()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        
        SmaileAuthentication auth = authOpt.get();
        User user = (User) auth.getPrincipal();
        
        // Build user profile with organization access
        UserProfile profile = UserProfile.builder()
            .userId(user.getId())
            .email(user.getEmail())
            .fullName(user.getFullName())
            .roles(auth.getAllRoles())
            .organizations(authorizationService.getAccessibleOrganizations(auth))
            .hasAdminAccess(authorizationService.hasAdministrativeAccess(auth))
            .build();
            
        return ResponseEntity.ok(profile);
    }
}
```

### 2. Permission-Based Access Control

```java
@RestController
public class OrganizationController {
    
    @Autowired
    private SmaileAuthorizationService authorizationService;
    
    @GetMapping("/organizations/{orgId}/patients")
    public ResponseEntity<List<Patient>> getPatients(@PathVariable UUID orgId) {
        Optional<SmaileAuthentication> authOpt = AuthenticationUtils.getCurrentAuthentication();
        
        if (authOpt.isEmpty()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        
        SmaileAuthentication auth = authOpt.get();
        
        // Check if user has permission to read patients in this organization
        if (!authorizationService.hasPermissionInOrganization(auth, orgId, "patient:read")) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        
        // User has permission, proceed with business logic
        List<Patient> patients = patientService.findByOrganization(orgId);
        return ResponseEntity.ok(patients);
    }
}
```

### 3. Role-Based Method Security

```java
@Service
public class PatientService {
    
    @PreAuthorize("@smaileAuthorizationService.hasRoleInOrganization(authentication, #organizationId, 'DOCTOR')")
    public Patient createPatient(UUID organizationId, CreatePatientRequest request) {
        // Only users with DOCTOR role in the organization can create patients
        return patientRepository.save(Patient.from(request));
    }
    
    @PreAuthorize("@smaileAuthorizationService.hasPermissionInOrganization(authentication, #organizationId, 'patient:manage')")
    public void deletePatient(UUID organizationId, UUID patientId) {
        // Only users with patient:manage permission can delete patients
        patientRepository.deleteById(patientId);
    }
}
```

### 4. Context Refresh on Permission Changes

```java
@Service
public class UserRoleService {
    
    @Autowired
    private AuthenticationContextService contextService;
    
    @Transactional
    public void assignRoleToUser(UUID userId, UUID organizationId, String roleCode) {
        // 1. Assign role in database
        UserRole userRole = UserRole.builder()
            .user(userService.findById(userId))
            .organization(organizationService.findById(organizationId))
            .role(roleService.findByCode(roleCode))
            .status(Status.ACTIVE)
            .build();
        userRoleRepository.save(userRole);
        
        // 2. Refresh authentication context if user is currently authenticated
        Optional<SmaileAuthentication> currentAuth = AuthenticationUtils.getCurrentAuthentication();
        if (currentAuth.isPresent()) {
            User currentUser = (User) currentAuth.get().getPrincipal();
            if (currentUser.getId().equals(userId)) {
                // Refresh the current user's authentication context
                SmaileAuthentication refreshedAuth = contextService.refreshAuthenticationContext(currentAuth.get());
                SecurityContextHolder.getContext().setAuthentication(refreshedAuth);
            }
        }
    }
}
```

## Performance Considerations

### 1. Caching Strategy

```java
// Organization permission contexts are cached per user
@Cacheable(value = "organizationPermissionContexts", key = "#user.keycloakId")
public Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(User user) {
    // Expensive operation - cache results
}

// Cache configuration
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30)) // 30-minute TTL
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

### 2. Lazy Loading and Optimization

```java
// Use projections to minimize database queries
@Query("SELECT uo.user.id as userId, " +
       "uo.organization.id as organizationId, " +
       "uo.organization.name as organizationName, " +
       "ur.role.code as roleCode, " +
       "ur.role.name as roleName " +
       "FROM UserRole ur JOIN ur.userOrganization uo " +
       "WHERE uo.user.id = :userId AND uo.status = 'ACTIVE'")
List<UserOrganizationRoles> findByUserId(@Param("userId") UUID userId);
```

## Security Best Practices

1. **Immutable Authentication Objects** - Prevent tampering after creation
2. **Comprehensive Validation** - Validate all inputs and contexts
3. **Secure Caching** - Use appropriate TTL and secure serialization
4. **Audit Logging** - Log all authentication and authorization events
5. **Fail-Safe Defaults** - Deny access when in doubt
6. **Context Refresh** - Update contexts when permissions change

This enhanced system provides a robust, scalable, and secure foundation for building user contexts with organization-specific permissions while following enterprise best practices.
